// Debug utilities for Payload CMS integration
// JavaScript version for Node.js compatibility

const DEBUG_MODE = process.env.NODE_ENV === 'development'

function debugLog(message, data) {
  if (DEBUG_MODE) {
    console.log(`🔍 [Payload Debug] ${message}`, data ? data : '')
  }
}

function debugError(message, error) {
  if (DEBUG_MODE) {
    console.error(`❌ [Payload Error] ${message}`, error ? error : '')
  }
}

function debugWarn(message, data) {
  if (DEBUG_MODE) {
    console.warn(`⚠️ [Payload Warn] ${message}`, data ? data : '')
  }
}

// Performance monitoring
function measurePerformance(label, fn) {
  return async () => {
    const start = performance.now()
    try {
      const result = await fn()
      const end = performance.now()
      debugLog(`${label} completed in ${(end - start).toFixed(2)}ms`)
      return result
    } catch (error) {
      const end = performance.now()
      debugError(`${label} failed after ${(end - start).toFixed(2)}ms`, error)
      throw error
    }
  }
}

// Payload-specific debugging
function debugPayloadQuery(collection, operation, params) {
  debugLog(`Payload Query: ${collection}.${operation}`, params)
}

function debugPayloadResult(collection, operation, result) {
  debugLog(`Payload Result: ${collection}.${operation}`, {
    count: Array.isArray(result) ? result.length : (result?.docs ? result.docs.length : 'N/A'),
    success: result ? true : false
  })
}

// Component debugging
function debugComponentRender(componentName, props) {
  debugLog(`Component Render: ${componentName}`, props ? Object.keys(props) : 'no props')
}

// API debugging
function debugAPIRequest(endpoint, method, params) {
  debugLog(`API Request: ${method} ${endpoint}`, params)
}

function debugAPIResponse(endpoint, status, data) {
  debugLog(`API Response: ${endpoint} - ${status}`, data ? 'data received' : 'no data')
}

export {
  DEBUG_MODE,
  debugLog,
  debugError,
  debugWarn,
  measurePerformance,
  debugPayloadQuery,
  debugPayloadResult,
  debugComponentRender,
  debugAPIRequest,
  debugAPIResponse
}
