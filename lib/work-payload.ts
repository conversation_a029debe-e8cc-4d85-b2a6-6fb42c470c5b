import { getPayload } from 'payload'
import config from '../payload.config'

export type PayloadWork = {
  id: string
  title: string
  slug: string
  subtitle?: string
  coverImage: {
    id: string
    filename: string
    alt?: string
  }
  date: string
  showOnHomepage: boolean
  passwordLock: boolean
  password?: string
  content: any // Rich text content
  gallery?: Array<{
    image: {
      id: string
      filename: string
      alt?: string
    }
    caption?: string
  }>
  createdAt: string
  updatedAt: string
}

export async function getAllWork(): Promise<PayloadWork[]> {
  try {
    const payload = await getPayload({ config })

    const work = await payload.find({
      collection: 'work',
      sort: '-date',
      limit: 100,
    })

    return work.docs as PayloadWork[]
  } catch (error) {
    console.error('Error fetching work from Payload:', error)
    return []
  }
}

export async function getWorkBySlug(slug: string): Promise<PayloadWork | null> {
  try {
    const payload = await getPayload({ config })

    const work = await payload.find({
      collection: 'work',
      where: {
        slug: {
          equals: slug,
        },
      },
      limit: 1,
    })

    return work.docs.length > 0 ? (work.docs[0] as PayloadWork) : null
  } catch (error) {
    console.error('Error fetching work by slug from Payload:', error)
    return null
  }
}

export async function getWorkForHomepage(): Promise<PayloadWork[]> {
  try {
    const payload = await getPayload({ config })

    const work = await payload.find({
      collection: 'work',
      where: {
        showOnHomepage: {
          equals: true,
        },
      },
      sort: '-date',
      limit: 6, // Show top 6 items on homepage
    })

    return work.docs as PayloadWork[]
  } catch (error) {
    console.error('Error fetching work for homepage from Payload:', error)
    return []
  }
}

export async function getAllWorkForAdmin(): Promise<PayloadWork[]> {
  try {
    const payload = await getPayload({ config })

    const work = await payload.find({
      collection: 'work',
      sort: '-date',
      limit: 100,
    })

    return work.docs as PayloadWork[]
  } catch (error) {
    console.error('Error fetching all work for admin from Payload:', error)
    return []
  }
}
