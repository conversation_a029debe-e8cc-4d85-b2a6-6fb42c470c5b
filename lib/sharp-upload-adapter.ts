import sharp from 'sharp'
import path from 'path'
import fs from 'fs/promises'

interface UploadConfig {
  staticDir: string
  staticURL?: string
  adminThumbnail?: string
  imageSizes?: Array<{
    name: string
    width?: number
    height?: number
    position?: string
  }>
  mimeTypes?: string[]
  upload?: (args: { file: any; filename: string; mimetype: string }) => Promise<any>
  delete?: (args: { filename: string }) => Promise<void>
}

const imageSizes = [
  {
    name: 'thumbnail',
    width: 400,
    height: 300,
    position: 'centre',
  },
  {
    name: 'card',
    width: 768,
    height: 1024,
    position: 'centre',
  },
  {
    name: 'hero',
    width: 1920,
    height: 1080,
    position: 'centre',
  },
]

export const sharpUploadAdapter: UploadConfig = {
  staticDir: path.join(process.cwd(), 'public', 'media'),
  staticURL: '/media',
  adminThumbnail: 'thumbnail',
  imageSizes,
  mimeTypes: ['image/*'],

  // Custom upload handler that uses <PERSON> for processing
  upload: async ({ file, filename, mimetype }) => {
    try {
      const outputDir = path.join(process.cwd(), 'public', 'media')
      const baseFilename = path.parse(filename).name
      const ext = path.parse(filename).ext

      // Ensure output directory exists
      await fs.mkdir(outputDir, { recursive: true })

      // Process original image
      const originalPath = path.join(outputDir, filename)
      await sharp(file.data)
        .jpeg({ quality: 90, progressive: true })
        .toFile(originalPath)

      // Generate responsive variants
      const variants = []

      for (const size of imageSizes) {
        if (size.name && size.width && size.height) {
          const variantFilename = `${baseFilename}_${size.name}${ext}`
          const variantPath = path.join(outputDir, variantFilename)

          await sharp(file.data)
            .resize(size.width, size.height, {
              fit: 'inside',
              withoutEnlargement: true,
              position: size.position as any,
            })
            .jpeg({ quality: 85, progressive: true })
            .toFile(variantPath)

          variants.push({
            name: size.name,
            filename: variantFilename,
            path: variantPath,
            width: size.width,
            height: size.height,
          })
        }
      }

      return {
        filename,
        filesize: file.size,
        width: (await sharp(file.data).metadata()).width,
        height: (await sharp(file.data).metadata()).height,
        mimeType: mimetype,
        variants,
      }
    } catch (error) {
      console.error('Sharp upload processing error:', error)
      throw error
    }
  },

  // Custom delete handler
  delete: async ({ filename }) => {
    try {
      const outputDir = path.join(process.cwd(), 'public', 'media')
      const filePath = path.join(outputDir, filename)

      // Delete original file
      try {
        await fs.unlink(filePath)
      } catch (error) {
        // File might not exist, continue
      }

      // Delete variants
      const baseFilename = path.parse(filename).name
      for (const size of imageSizes) {
        if (size.name) {
          const variantFilename = `${baseFilename}_${size.name}${path.parse(filename).ext}`
          const variantPath = path.join(outputDir, variantFilename)

          try {
            await fs.unlink(variantPath)
          } catch (error) {
            // Variant might not exist, continue
          }
        }
      }
    } catch (error) {
      console.error('Sharp delete error:', error)
      throw error
    }
  },
}
