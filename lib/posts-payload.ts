import { getPayload } from 'payload'
import config from '../payload.config'

export type PayloadPost = {
  id: string
  title: string
  slug: string
  excerpt?: string
  content: any // Rich text content
  date: string
  visibility: boolean
  author?: string
  coverImage?: {
    id: string
    filename: string
    alt?: string
  }
  tags?: Array<{
    tag: string
  }>
  createdAt: string
  updatedAt: string
}

export async function getAllPosts(): Promise<PayloadPost[]> {
  try {
    const payload = await getPayload({ config })

    const posts = await payload.find({
      collection: 'posts',
      where: {
        visibility: {
          equals: true,
        },
      },
      sort: '-date',
      limit: 100,
    })

    return posts.docs as PayloadPost[]
  } catch (error) {
    console.error('Error fetching posts from Payload:', error)
    return []
  }
}

export async function getPostBySlug(slug: string): Promise<PayloadPost | null> {
  try {
    const payload = await getPayload({ config })

    const posts = await payload.find({
      collection: 'posts',
      where: {
        slug: {
          equals: slug,
        },
        visibility: {
          equals: true,
        },
      },
      limit: 1,
    })

    return posts.docs.length > 0 ? (posts.docs[0] as PayloadPost) : null
  } catch (error) {
    console.error('Error fetching post by slug from Payload:', error)
    return null
  }
}

export async function getAllPostsForAdmin(): Promise<PayloadPost[]> {
  try {
    const payload = await getPayload({ config })

    const posts = await payload.find({
      collection: 'posts',
      sort: '-date',
      limit: 100,
    })

    return posts.docs as PayloadPost[]
  } catch (error) {
    console.error('Error fetching all posts for admin from Payload:', error)
    return []
  }
}
