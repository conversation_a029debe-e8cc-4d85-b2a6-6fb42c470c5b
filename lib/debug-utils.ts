// Debug utilities for Payload CMS integration
// These utilities help with debugging and monitoring the CMS integration

export const DEBUG_MODE = process.env.NODE_ENV === 'development'

export function debugLog(message: string, data?: any) {
  if (DEBUG_MODE) {
    console.log(`🔍 [Payload Debug] ${message}`, data ? data : '')
  }
}

export function debugError(message: string, error?: any) {
  if (DEBUG_MODE) {
    console.error(`❌ [Payload Error] ${message}`, error ? error : '')
  }
}

export function debugWarn(message: string, data?: any) {
  if (DEBUG_MODE) {
    console.warn(`⚠️ [Payload Warn] ${message}`, data ? data : '')
  }
}

// Performance monitoring
export function measurePerformance(label: string, fn: () => Promise<any>) {
  return async () => {
    const start = performance.now()
    try {
      const result = await fn()
      const end = performance.now()
      debugLog(`${label} completed in ${(end - start).toFixed(2)}ms`)
      return result
    } catch (error) {
      const end = performance.now()
      debugError(`${label} failed after ${(end - start).toFixed(2)}ms`, error)
      throw error
    }
  }
}

// Payload-specific debugging
export function debugPayloadQuery(collection: string, operation: string, params?: any) {
  debugLog(`Payload Query: ${collection}.${operation}`, params)
}

export function debugPayloadResult(collection: string, operation: string, result: any) {
  debugLog(`Payload Result: ${collection}.${operation}`, {
    count: Array.isArray(result) ? result.length : (result?.docs ? result.docs.length : 'N/A'),
    success: result ? true : false
  })
}

// Component debugging
export function debugComponentRender(componentName: string, props?: any) {
  debugLog(`Component Render: ${componentName}`, props ? Object.keys(props) : 'no props')
}

// API debugging
export function debugAPIRequest(endpoint: string, method: string, params?: any) {
  debugLog(`API Request: ${method} ${endpoint}`, params)
}

export function debugAPIResponse(endpoint: string, status: number, data?: any) {
  debugLog(`API Response: ${endpoint} - ${status}`, data ? 'data received' : 'no data')
}

// Browser console utilities for debugging
export const browserDebugUtils = `
  // Browser console debugging utilities
  window.debugPayload = {
    // Check if Payload is loaded
    checkPayload: () => {
      console.log('🔍 Checking Payload status...')
      if (typeof window !== 'undefined') {
        console.log('✅ Running in browser environment')
      }
      return true
    },

    // Test admin panel access
    testAdminAccess: () => {
      console.log('🔍 Testing admin panel access...')
      fetch('/api/admin/dashboard')
        .then(res => {
          console.log('📊 Admin dashboard response:', res.status)
          return res.text()
        })
        .then(text => console.log('📄 Admin dashboard HTML length:', text.length))
        .catch(err => console.error('❌ Admin access error:', err))
    },

    // Test media API
    testMediaAPI: () => {
      console.log('🔍 Testing media API...')
      fetch('/api/admin/media/list')
        .then(res => {
          console.log('📊 Media API response:', res.status)
          return res.text()
        })
        .then(text => console.log('📄 Media list HTML length:', text.length))
        .catch(err => console.error('❌ Media API error:', err))
    },

    // Test posts API
    testPostsAPI: () => {
      console.log('🔍 Testing posts API...')
      fetch('/api/posts')
        .then(res => {
          console.log('📊 Posts API response:', res.status)
          return res.json()
        })
        .then(data => console.log('📄 Posts data:', data))
        .catch(err => console.error('❌ Posts API error:', err))
    },

    // Test work API
    testWorkAPI: () => {
      console.log('🔍 Testing work API...')
      fetch('/api/work')
        .then(res => {
          console.log('📊 Work API response:', res.status)
          return res.json()
        })
        .then(data => console.log('📄 Work data:', data))
        .catch(err => console.error('❌ Work API error:', err))
    },

    // Test page rendering
    testPageRender: () => {
      console.log('🔍 Testing page rendering...')
      // Check if PageRenderer components are present
      const pageRenderer = document.querySelector('[data-page-renderer]')
      if (pageRenderer) {
        console.log('✅ PageRenderer found')
      } else {
        console.log('⚠️ PageRenderer not found, might be using fallback')
      }
    },

    // Run all tests
    runAllTests: () => {
      console.log('🚀 Running all Payload CMS tests...')
      window.debugPayload.checkPayload()
      window.debugPayload.testAdminAccess()
      window.debugPayload.testMediaAPI()
      window.debugPayload.testPostsAPI()
      window.debugPayload.testWorkAPI()
      window.debugPayload.testPageRender()
      console.log('✅ All tests completed!')
    }
  }

  console.log('🔧 Payload Debug Utils loaded! Run window.debugPayload.runAllTests() to test everything.')
`

// Inject debug utilities into the browser
export function injectBrowserDebugUtils() {
  if (typeof window !== 'undefined') {
    // Create script element and inject debug utilities
    const script = document.createElement('script')
    script.textContent = browserDebugUtils
    document.head.appendChild(script)
  }
}
