import { getPayload } from 'payload'
import config from '../payload.config'

export type PayloadPage = {
  id: string
  title: string
  slug: string
  layout: Array<{
    blockType: string
    [key: string]: any
  }>
  createdAt: string
  updatedAt: string
}

export async function getPageBySlug(slug: string): Promise<PayloadPage | null> {
  try {
    const payload = await getPayload({ config })

    const pages = await payload.find({
      collection: 'pages',
      where: {
        slug: {
          equals: slug,
        },
      },
      limit: 1,
    })

    return pages.docs.length > 0 ? (pages.docs[0] as PayloadPage) : null
  } catch (error) {
    console.error('Error fetching page by slug from Payload:', error)
    return null
  }
}

export async function getHomepageLayout(): Promise<PayloadPage | null> {
  return getPageBySlug('home')
}

export async function getAllPages(): Promise<PayloadPage[]> {
  try {
    const payload = await getPayload({ config })

    const pages = await payload.find({
      collection: 'pages',
      sort: 'title',
      limit: 100,
    })

    return pages.docs as PayloadPage[]
  } catch (error) {
    console.error('Error fetching all pages from Payload:', error)
    return []
  }
}
