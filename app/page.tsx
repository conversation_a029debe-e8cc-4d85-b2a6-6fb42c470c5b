import { PageRenderer } from '../components/PageRenderer'
import { getHomepageLayout } from '../lib/pages-payload'

export default async function PersonalPage() {
  // Try to get the homepage layout from Payload
  const homepage = await getHomepageLayout()

  // If homepage exists in Payload, use the PageRenderer
  if (homepage) {
    return <PageRenderer page={homepage} />
  }

  // Fallback to the original components if no Payload homepage exists
  const { PersonalClient } = await import('./components/PersonalClient')
  const { WorkSectionServer } = await import('./components/WorkSectionServer')

  return <PersonalClient workSection={<WorkSectionServer />} />
}
