'use client'

import { useEffect, useState } from 'react'

export default function AdminPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Check if we're on the root admin path
    if (window.location.pathname === '/admin' || window.location.pathname === '/admin/') {
      // Let the route handler serve the login page
      setIsLoading(false)
    } else {
      // For other admin routes, redirect to root
      window.location.href = '/admin'
    }
  }, [])

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-zinc-950">
        <div className="text-center">
          <div className="text-red-400 text-lg mb-4">Error Loading Admin Panel</div>
          <p className="text-zinc-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-white text-black rounded hover:bg-zinc-200"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-zinc-950">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto"></div>
          <p className="mt-4 text-white">Loading Payload Admin...</p>
          <p className="mt-2 text-zinc-400 text-sm">
            Initializing admin panel
          </p>
        </div>
      </div>
    )
  }

  // Return null to let the route handler take over
  return null
}
