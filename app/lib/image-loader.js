/**
 * Enhanced image loader for Next.js static export with context-aware responsive images
 * This is referenced in next.config.mjs and provides both src and srcset generation
 */

// DPI-focused image configurations (Intercom style - matches optimize-images.js)
const IMAGE_CONTEXTS = {
  thumbnail: {
    baseWidth: 800,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 800,
    sizesAttr: '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw',
  },
  regular: {
    baseWidth: 800,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 800,
    sizesAttr: '(max-width: 640px) 100vw, (max-width: 1024px) 90vw, 800px',
  },
  mid: {
    baseWidth: 1200,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 1200,
    sizesAttr: '(max-width: 640px) 100vw, (max-width: 1024px) 90vw, 1200px',
  },
  wide: {
    baseWidth: 1632,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 1632,
    sizesAttr: '100vw',
  },
  sideBySide: {
    baseWidth: 600,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 600,
    sizesAttr: '(max-width: 640px) 100vw, (max-width: 1024px) 45vw, 400px',
  },
  carousel: {
    baseWidth: 800,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 800,
    sizesAttr: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  },
};

// Detect image context from filename and requested width
function detectImageContext(src, width) {
  const fileName = src.split('/').pop() || '';

  // Detect by filename patterns
  if (fileName.includes('thumb_')) return 'thumbnail';
  if (fileName.includes('carousel')) return 'carousel';

  // Detect by requested width (fallback) - simplified for DPI approach
  if (width <= 600) return 'sideBySide';
  if (width <= 800) return 'regular';
  if (width <= 1200) return 'mid';
  if (width > 1200) return 'wide';

  return 'regular';
}

/**
 * Generate DPI-based srcset for a given image and context (Intercom style)
 * @param {string} src - Image source path
 * @param {string} context - Image context (thumbnail, regular, mid, wide, etc.)
 * @param {boolean} isDevelopment - Whether we're in development mode
 * @returns {string} - DPI-based srcset string
 */
function generateDPISrcSet(src, context, isDevelopment) {
  const contextConfig = IMAGE_CONTEXTS[context];
  const baseName = src.replace(/\.[^.]+$/, '');
  const originalExt = src.match(/\.(jpe?g|png|gif|webp|avif)($|\?)/i);
  const ext = originalExt ? originalExt[1].toLowerCase() : 'jpg';

  // Use pre-generated DPI variants in both dev and production
  // Prefer AVIF > WebP > Original format
  const formats = ['avif', 'webp', ext];
  const bestFormat =
    formats.find((format) => format === 'avif') ||
    formats.find((format) => format === 'webp') ||
    ext;

  return contextConfig.dpi
    .map((dpr) => {
      const dpiInfo = dpr > 1 ? `_${dpr}x` : `_1x`;
      return `${baseName}${dpiInfo}.${bestFormat} ${dpr}x`;
    })
    .join(', ');
}

/**
 * Legacy function - now redirects to generateDPISrcSet for consistency
 * @param {string} src - Image source path
 * @param {string} context - Image context
 * @param {number} baseWidth - Base width for density calculations (ignored in new DPI approach)
 * @param {boolean} isDevelopment - Whether we're in development mode
 * @returns {string} - DPI-based srcset string
 */
function generateHiDPISrcSet(src, context, baseWidth, isDevelopment) {
  // Redirect to new DPI-focused approach
  return generateDPISrcSet(src, context, isDevelopment);
}

export default function imageLoader({ src, width, quality }) {
  // Handle absolute URLs (remote images)
  if (src.startsWith('http')) {
    return src;
  }

  // Process local images
  // Make sure the source points to the public/images directory
  if (!src.startsWith('/images/') && src.startsWith('/')) {
    src = `/images${src}`;
  } else if (!src.startsWith('/')) {
    src = `/images/${src}`;
  }

  // Check if we're in development mode
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Check if we can use WebP for this image
  const originalExt = src.match(/\.(jpe?g|png|gif|webp|avif)($|\?)/i);
  const ext = originalExt ? originalExt[1].toLowerCase() : 'jpg';

  // Use context-aware DPI images in both dev and production
  const context = detectImageContext(src, width);
  const contextConfig = IMAGE_CONTEXTS[context];

  // Use base width from context (simplified DPI approach)
  const baseWidth = contextConfig.baseWidth;

  // Use appropriate quality (default to 1x quality)
  const targetQuality = contextConfig.quality[1];

  // Prefer AVIF > WebP > Original format
  let optimizedSrc = src;
  const baseName = src.replace(/\.[^.]+$/, '');

  // Try AVIF first, then WebP, then original
  const formats = ['avif', 'webp', ext];
  let bestFormat = 'avif'; // Default to AVIF

  // Check if we have AVIF support, fallback to WebP, then original
  if (ext !== 'avif') {
    optimizedSrc = `${baseName}.avif`;
    bestFormat = 'avif';
  } else if (ext !== 'webp') {
    optimizedSrc = `${baseName}.webp`;
    bestFormat = 'webp';
  } else {
    bestFormat = ext;
  }

  // Generate the DPI variant filename (default to 1x)
  const dpiInfo = '_1x';
  const finalSrc = optimizedSrc.replace(/(\.[^.]+)$/, `${dpiInfo}$1`);

  // Clean URL without query parameters and return
  return finalSrc.split('?')[0];
};

// Export additional functions for use by components
export { generateDPISrcSet, generateHiDPISrcSet, detectImageContext, IMAGE_CONTEXTS };
