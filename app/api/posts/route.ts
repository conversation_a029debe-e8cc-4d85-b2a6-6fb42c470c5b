import { getPayload } from 'payload'
import config from '../../../payload.config'
import { NextRequest } from 'next/server'

export async function GET() {
  try {
    const payload = await getPayload({ config })

    const posts = await payload.find({
      collection: 'posts',
      limit: 100,
      sort: '-date',
      where: {
        visibility: {
          equals: true,
        },
      },
    })

    return new Response(JSON.stringify(posts), {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    console.error('Posts API error:', error)
    return new Response(JSON.stringify({ error: 'Failed to fetch posts' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // Format content for Payload's rich text editor
    const formattedContent = {
      root: {
        type: 'root',
        format: '',
        indent: 0,
        version: 1,
        children: [
          {
            type: 'paragraph',
            format: '',
            indent: 0,
            version: 1,
            children: [
              {
                mode: 'normal',
                text: body.content || '',
                type: 'text',
                style: '',
                detail: 0,
                format: 0,
                version: 1,
              },
            ],
          },
        ],
        direction: 'ltr',
      },
    }

    const post = await payload.create({
      collection: 'posts',
      data: {
        title: body.title,
        slug: body.slug,
        excerpt: body.excerpt || '',
        content: formattedContent,
        date: body.date,
        visibility: body.visibility !== false,
        author: body.author || '',
        tags: Array.isArray(body.tags) ? body.tags.map(tag => ({ tag })) : [],
      },
    })

    return new Response(JSON.stringify(post), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    console.error('Post creation error:', error)
    return new Response(JSON.stringify({
      error: 'Failed to create post',
      message: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}
