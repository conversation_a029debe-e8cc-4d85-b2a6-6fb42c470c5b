import { NextRequest } from 'next/server'
import { getPayload } from 'payload'
import config from '../../../payload.config'

export async function GET(request: NextRequest) {
  const payload = await getPayload({ config })
  return new Response(JSON.stringify({ message: 'Payload API is running' }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' },
  })
}

export async function POST(request: NextRequest) {
  const payload = await getPayload({ config })
  return new Response(JSON.stringify({ message: 'Payload API is running' }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' },
  })
}
