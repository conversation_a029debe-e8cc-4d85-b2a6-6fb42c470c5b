import { getPayload } from 'payload'
import config from '../../../../payload.config'
import { NextRequest } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Extract the admin route from the URL
    const url = new URL(request.url)
    const segments = url.pathname.replace('/api/admin/', '').split('/').filter(Boolean)

    // Handle different admin routes
    if (segments.length === 0) {
      // Root admin path - redirect to dashboard
      return new Response(null, {
        status: 302,
        headers: {
          Location: '/api/admin/dashboard',
        },
      })
    }

    const [collection, action, id] = segments

    // Handle dashboard
    if (collection === 'dashboard') {
      const dashboardHTML = `
        <!DOCTYPE html>
        <html>
          <head>
            <title>Payload Admin Dashboard</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
              body {
                margin: 0;
                padding: 20px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: #09090b;
                color: white;
              }
              .container {
                max-width: 1200px;
                margin: 0 auto;
              }
              .header {
                text-align: center;
                margin-bottom: 40px;
                padding-bottom: 20px;
                border-bottom: 1px solid #374151;
              }
              .grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-bottom: 40px;
              }
              .card {
                background: #1a1a1a;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                text-align: center;
              }
              .card h3 {
                margin: 0 0 10px 0;
                color: #60a5fa;
              }
              .card p {
                margin: 0 0 20px 0;
                color: #9ca3af;
              }
              .btn {
                display: inline-block;
                padding: 10px 20px;
                background: white;
                color: black;
                text-decoration: none;
                border-radius: 4px;
                font-weight: 500;
                transition: background 0.2s;
              }
              .btn:hover {
                background: #e5e5e5;
              }
              .logout {
                position: absolute;
                top: 20px;
                right: 20px;
              }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="logout">
                <a href="/api/admin/logout" class="btn">Logout</a>
              </div>

              <div class="header">
                <h1>Payload Admin Dashboard</h1>
                <p>Welcome to your content management system</p>
              </div>

              <div class="grid">
                <div class="card">
                  <h3>📝 Posts</h3>
                  <p>Manage blog posts and articles</p>
                  <a href="/admin/posts" class="btn">Manage Posts</a>
                </div>

                <div class="card">
                  <h3>💼 Work</h3>
                  <p>Manage portfolio projects</p>
                  <a href="/admin/work" class="btn">Manage Work</a>
                </div>

                <div class="card">
                  <h3>🖼️ Media</h3>
                  <p>Upload and manage images</p>
                  <a href="/admin/media" class="btn">Manage Media</a>
                </div>

                <div class="card">
                  <h3>📄 Pages</h3>
                  <p>Create and edit page layouts</p>
                  <a href="/admin/pages" class="btn">Manage Pages</a>
                </div>

                <div class="card">
                  <h3>👥 Users</h3>
                  <p>Manage admin users</p>
                  <a href="/admin/users" class="btn">Manage Users</a>
                </div>

                <div class="card">
                  <h3>⚙️ Settings</h3>
                  <p>Configure system settings</p>
                  <a href="/api/admin/settings" class="btn">System Settings</a>
                </div>
              </div>
            </div>
          </body>
        </html>
      `

      return new Response(dashboardHTML, {
        headers: {
          'Content-Type': 'text/html',
        },
      })
    }

    // Handle settings
    if (collection === 'settings') {
      const settingsHTML = `
        <!DOCTYPE html>
        <html>
          <head>
            <title>System Settings - Payload Admin</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
              body {
                margin: 0;
                padding: 20px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: #09090b;
                color: white;
              }
              .container {
                max-width: 800px;
                margin: 0 auto;
              }
              .header {
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 1px solid #374151;
              }
              .back-btn {
                display: inline-block;
                padding: 8px 16px;
                background: #374151;
                color: white;
                text-decoration: none;
                border-radius: 4px;
                margin-bottom: 20px;
              }
              .back-btn:hover {
                background: #4b5563;
              }
              .settings-card {
                background: #1a1a1a;
                border-radius: 8px;
                padding: 30px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
              }
              .setting-group {
                margin-bottom: 30px;
              }
              .setting-title {
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 15px;
                color: #60a5fa;
              }
              .setting-info {
                color: #9ca3af;
                margin-bottom: 15px;
              }
              .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
              }
              .info-item {
                background: #2a2a2a;
                padding: 15px;
                border-radius: 4px;
              }
              .info-label {
                font-weight: 500;
                margin-bottom: 5px;
                color: #e5e7eb;
              }
              .info-value {
                color: #60a5fa;
                font-family: monospace;
              }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <a href="/api/admin/dashboard" class="back-btn">← Back to Dashboard</a>
                <h1>System Settings</h1>
              </div>

              <div class="settings-card">
                <div class="setting-group">
                  <h3 class="setting-title">Database Information</h3>
                  <div class="info-grid">
                    <div class="info-item">
                      <div class="info-label">Database Type</div>
                      <div class="info-value">SQLite</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">Database File</div>
                      <div class="info-value">./payload.db</div>
                    </div>
                  </div>
                </div>

                <div class="setting-group">
                  <h3 class="setting-title">Collections</h3>
                  <div class="info-grid">
                    <div class="info-item">
                      <div class="info-label">Posts Collection</div>
                      <div class="info-value">Active</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">Work Collection</div>
                      <div class="info-value">Active</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">Media Collection</div>
                      <div class="info-value">Active</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">Pages Collection</div>
                      <div class="info-value">Active</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">Users Collection</div>
                      <div class="info-value">Active</div>
                    </div>
                  </div>
                </div>

                <div class="setting-group">
                  <h3 class="setting-title">System Status</h3>
                  <div class="info-grid">
                    <div class="info-item">
                      <div class="info-label">Payload CMS</div>
                      <div class="info-value">v3.54.0</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">Next.js</div>
                      <div class="info-value">v15.3.0</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">Node.js</div>
                      <div class="info-value">v20.19.1</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">Status</div>
                      <div class="info-value" style="color: #10b981;">Running</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </body>
        </html>
      `

      return new Response(settingsHTML, {
        headers: {
          'Content-Type': 'text/html',
        },
      })
    }

    // Handle logout
    if (collection === 'logout') {
      return new Response(null, {
        status: 302,
        headers: {
          Location: '/admin',
          'Set-Cookie': 'payload-token=; Path=/; HttpOnly; Max-Age=0',
        },
      })
    }

    // For other routes, redirect to dashboard
    return new Response(null, {
      status: 302,
      headers: {
        Location: '/api/admin/dashboard',
      },
    })

  } catch (error) {
    console.error('Admin route error:', error)
    return new Response('Admin panel error', { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // Handle login
    if (request.nextUrl.pathname === '/api/admin/login') {
      try {
        const result = await payload.login({
          collection: 'users',
          data: {
            email: body.email,
            password: body.password,
          },
        })

        if (result.token) {
          return new Response(JSON.stringify({
            success: true,
            token: result.token,
            user: result.user,
          }), {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
            },
          })
        } else {
          return new Response(JSON.stringify({
            success: false,
            message: 'Invalid credentials',
          }), {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
            },
          })
        }
      } catch (error) {
        return new Response(JSON.stringify({
          success: false,
          message: 'Login failed',
        }), {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        })
      }
    }

    return new Response(JSON.stringify({ message: 'Method not allowed' }), {
      status: 405,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    console.error('Admin POST error:', error)
    return new Response(JSON.stringify({ message: 'Server error' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}
