import { getPayload } from 'payload'
import config from '../../../../../payload.config'
import { NextRequest } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return new Response(JSON.stringify({ error: 'No file provided' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      })
    }

    // Convert File to buffer for Sharp processing
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // Create media document in Payload
    const mediaDoc = await payload.create({
      collection: 'media',
      data: {
        alt: formData.get('alt') || file.name,
        caption: formData.get('caption') || '',
      },
      file: {
        name: file.name,
        data: buffer,
        mimetype: file.type,
        size: file.size,
      },
    })

    return new Response(JSON.stringify(mediaDoc), {
      status: 201,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('Media upload error:', error)
    return new Response(JSON.stringify({
      error: 'Failed to upload media',
      message: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    })
  }
}
