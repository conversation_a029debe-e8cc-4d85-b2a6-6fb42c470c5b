import { getPayload } from 'payload'
import config from '../../../../../payload.config'

export async function GET() {
  try {
    const payload = await getPayload({ config })

    // Get all media files
    const media = await payload.find({
      collection: 'media',
      limit: 100,
      sort: '-createdAt',
    })

    const listHTML = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Media Library - Payload Admin</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body {
              margin: 0;
              padding: 20px;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              background: #09090b;
              color: white;
            }
            .container {
              max-width: 1200px;
              margin: 0 auto;
            }
            .header {
              margin-bottom: 30px;
              padding-bottom: 20px;
              border-bottom: 1px solid #374151;
            }
            .back-btn {
              display: inline-block;
              padding: 8px 16px;
              background: #374151;
              color: white;
              text-decoration: none;
              border-radius: 4px;
              margin-bottom: 20px;
            }
            .back-btn:hover {
              background: #4b5563;
            }
            .upload-btn {
              display: inline-block;
              padding: 10px 20px;
              background: #60a5fa;
              color: white;
              text-decoration: none;
              border-radius: 4px;
              margin-bottom: 20px;
            }
            .upload-btn:hover {
              background: #3b82f6;
            }
            .media-grid {
              display: grid;
              grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
              gap: 20px;
            }
            .media-card {
              background: #1a1a1a;
              border-radius: 8px;
              padding: 15px;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .media-image {
              width: 100%;
              height: 200px;
              object-fit: cover;
              border-radius: 4px;
              margin-bottom: 10px;
            }
            .media-info h4 {
              margin: 0 0 5px 0;
              color: #60a5fa;
              font-size: 14px;
              word-break: break-all;
            }
            .media-meta {
              font-size: 12px;
              color: #6b7280;
              margin-bottom: 10px;
            }
            .media-actions {
              display: flex;
              gap: 8px;
            }
            .btn-small {
              padding: 4px 8px;
              border-radius: 3px;
              text-decoration: none;
              font-size: 11px;
              font-weight: 500;
            }
            .btn-copy {
              background: #10b981;
              color: white;
            }
            .btn-copy:hover {
              background: #059669;
            }
            .btn-delete {
              background: #ef4444;
              color: white;
            }
            .btn-delete:hover {
              background: #dc2626;
            }
            .empty-state {
              text-align: center;
              padding: 60px 20px;
              color: #6b7280;
            }
            .upload-form {
              background: #1a1a1a;
              border-radius: 8px;
              padding: 20px;
              margin-bottom: 30px;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .form-group {
              margin-bottom: 15px;
            }
            .form-row {
              display: flex;
              gap: 15px;
            }
            .form-row .form-group {
              flex: 1;
            }
            label {
              display: block;
              margin-bottom: 5px;
              font-weight: 500;
              color: #e5e7eb;
            }
            input, textarea {
              width: 100%;
              padding: 8px;
              border: 1px solid #374151;
              border-radius: 4px;
              background: #2a2a2a;
              color: white;
              font-size: 14px;
              box-sizing: border-box;
            }
            input:focus, textarea:focus {
              outline: none;
              border-color: #60a5fa;
            }
            .submit-btn {
              padding: 10px 20px;
              background: #60a5fa;
              color: white;
              border: none;
              border-radius: 4px;
              font-size: 14px;
              font-weight: 500;
              cursor: pointer;
            }
            .submit-btn:hover {
              background: #3b82f6;
            }
            .submit-btn:disabled {
              background: #374151;
              cursor: not-allowed;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <a href="/api/admin/dashboard" class="back-btn">← Back to Dashboard</a>
              <h1>Media Library (${media.docs.length})</h1>
              <a href="#upload" class="upload-btn">Upload New Media</a>
            </div>

            <div class="upload-form" id="upload">
              <h3>Upload New Media</h3>
              <form id="uploadForm" enctype="multipart/form-data">
                <div class="form-row">
                  <div class="form-group">
                    <label for="file">File *</label>
                    <input type="file" id="file" name="file" accept="image/*" required>
                  </div>
                  <div class="form-group">
                    <label for="alt">Alt Text *</label>
                    <input type="text" id="alt" name="alt" required>
                  </div>
                </div>
                <div class="form-group">
                  <label for="caption">Caption</label>
                  <textarea id="caption" name="caption" rows="2"></textarea>
                </div>
                <button type="submit" class="submit-btn">Upload Media</button>
              </form>
            </div>

            ${media.docs.length > 0 ? `
              <div class="media-grid">
                ${media.docs.map(item => `
                  <div class="media-card">
                    <img src="/media/${item.filename}" alt="${item.alt}" class="media-image">
                    <div class="media-info">
                      <h4>${item.filename}</h4>
                      <div class="media-meta">
                        ${item.width}x${item.height} • ${(item.filesize / 1024).toFixed(1)}KB<br>
                        ${new Date(item.createdAt).toLocaleDateString()}
                      </div>
                      <div class="media-actions">
                        <button class="btn-small btn-copy" onclick="copyUrl('/media/${item.filename}')">Copy URL</button>
                        <a href="/api/admin/media/delete/${item.id}" class="btn-small btn-delete" onclick="return confirm('Are you sure you want to delete this media file?')">Delete</a>
                      </div>
                    </div>
                  </div>
                `).join('')}
              </div>
            ` : `
              <div class="empty-state">
                <h3>No media files found</h3>
                <p>Upload your first image or media file to get started.</p>
              </div>
            `}
          </div>

          <script>
            // Auto-fill alt text from filename
            document.getElementById('file').addEventListener('change', function(e) {
              const file = e.target.files[0];
              if (file) {
                const altInput = document.getElementById('alt');
                if (!altInput.value) {
                  altInput.value = file.name.replace(/\\.[^/.]+$/, '').replace(/[-_]/g, ' ');
                }
              }
            });

            // Handle form submission
            document.getElementById('uploadForm').addEventListener('submit', async (e) => {
              e.preventDefault();

              const submitBtn = document.querySelector('.submit-btn');
              const fileInput = document.getElementById('file');
              const altInput = document.getElementById('alt');

              if (!fileInput.files[0]) {
                alert('Please select a file to upload');
                return;
              }

              if (!altInput.value.trim()) {
                alert('Please provide alt text for the image');
                return;
              }

              submitBtn.disabled = true;
              submitBtn.textContent = 'Uploading...';

              const formData = new FormData();
              formData.append('file', fileInput.files[0]);
              formData.append('alt', altInput.value);
              formData.append('caption', document.getElementById('caption').value);

              try {
                const response = await fetch('/api/admin/media/upload', {
                  method: 'POST',
                  body: formData,
                });

                if (response.ok) {
                  alert('Media uploaded successfully!');
                  window.location.reload();
                } else {
                  const errorData = await response.json();
                  alert('Upload failed: ' + (errorData.error || 'Unknown error'));
                  submitBtn.disabled = false;
                  submitBtn.textContent = 'Upload Media';
                }
              } catch (err) {
                alert('Network error. Please try again.');
                submitBtn.disabled = false;
                submitBtn.textContent = 'Upload Media';
              }
            });

            // Copy URL to clipboard
            function copyUrl(url) {
              navigator.clipboard.writeText(window.location.origin + url).then(() => {
                alert('URL copied to clipboard!');
              }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = window.location.origin + url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('URL copied to clipboard!');
              });
            }
          </script>
        </body>
      </html>
    `

    return new Response(listHTML, {
      headers: {
        'Content-Type': 'text/html',
      },
    })
  } catch (error) {
    console.error('Media list error:', error)
    return new Response('Error loading media', { status: 500 })
  }
}
