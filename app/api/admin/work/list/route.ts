import { getPayload } from 'payload'
import config from '../../../../../payload.config'

export async function GET() {
  try {
    const payload = await getPayload({ config })

    // Get all work items
    const workItems = await payload.find({
      collection: 'work',
      limit: 100,
      sort: '-date',
    })

    const listHTML = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Work Items - Payload Admin</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body {
              margin: 0;
              padding: 20px;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              background: #09090b;
              color: white;
            }
            .container {
              max-width: 1200px;
              margin: 0 auto;
            }
            .header {
              margin-bottom: 30px;
              padding-bottom: 20px;
              border-bottom: 1px solid #374151;
            }
            .back-btn {
              display: inline-block;
              padding: 8px 16px;
              background: #374151;
              color: white;
              text-decoration: none;
              border-radius: 4px;
              margin-bottom: 20px;
            }
            .back-btn:hover {
              background: #4b5563;
            }
            .create-btn {
              display: inline-block;
              padding: 10px 20px;
              background: #60a5fa;
              color: white;
              text-decoration: none;
              border-radius: 4px;
              margin-bottom: 20px;
            }
            .create-btn:hover {
              background: #3b82f6;
            }
            .work-grid {
              display: grid;
              grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
              gap: 20px;
            }
            .work-card {
              background: #1a1a1a;
              border-radius: 8px;
              padding: 20px;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .work-title {
              font-size: 18px;
              font-weight: 600;
              margin: 0 0 10px 0;
              color: #60a5fa;
            }
            .work-subtitle {
              color: #9ca3af;
              margin: 0 0 15px 0;
              font-size: 14px;
            }
            .work-meta {
              font-size: 12px;
              color: #6b7280;
              margin-bottom: 15px;
            }
            .work-actions {
              display: flex;
              gap: 10px;
            }
            .btn-small {
              padding: 6px 12px;
              border-radius: 4px;
              text-decoration: none;
              font-size: 12px;
              font-weight: 500;
            }
            .btn-edit {
              background: #10b981;
              color: white;
            }
            .btn-edit:hover {
              background: #059669;
            }
            .btn-delete {
              background: #ef4444;
              color: white;
            }
            .btn-delete:hover {
              background: #dc2626;
            }
            .empty-state {
              text-align: center;
              padding: 60px 20px;
              color: #6b7280;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <a href="/api/admin/dashboard" class="back-btn">← Back to Dashboard</a>
              <h1>Work Items (${workItems.docs.length})</h1>
              <a href="/api/admin/work/create" class="create-btn">+ Create New Work Item</a>
            </div>

            ${workItems.docs.length > 0 ? `
              <div class="work-grid">
                ${workItems.docs.map(work => `
                  <div class="work-card">
                    <h3 class="work-title">${work.title}</h3>
                    ${work.subtitle ? `<p class="work-subtitle">${work.subtitle}</p>` : ''}
                    <div class="work-meta">
                      Date: ${new Date(work.date).toLocaleDateString()}<br>
                      ${work.showOnHomepage ? 'Visible on homepage' : 'Hidden from homepage'}
                      ${work.passwordLock ? ' • Password protected' : ''}
                    </div>
                    <div class="work-actions">
                      <a href="/api/admin/work/edit/${work.id}" class="btn-small btn-edit">Edit</a>
                      <a href="/api/admin/work/delete/${work.id}" class="btn-small btn-delete" onclick="return confirm('Are you sure you want to delete this work item?')">Delete</a>
                    </div>
                  </div>
                `).join('')}
              </div>
            ` : `
              <div class="empty-state">
                <h3>No work items found</h3>
                <p>Create your first work item to get started.</p>
                <a href="/api/admin/work/create" class="create-btn">Create First Work Item</a>
              </div>
            `}
          </div>
        </body>
      </html>
    `

    return new Response(listHTML, {
      headers: {
        'Content-Type': 'text/html',
      },
    })
  } catch (error) {
    console.error('Work list error:', error)
    return new Response('Error loading work items', { status: 500 })
  }
}
