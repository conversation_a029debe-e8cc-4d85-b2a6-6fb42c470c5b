import { NextRequest } from 'next/server'
import { getPayload } from 'payload'
import config from '../../../../../../payload.config'

function escapeHtml(input: string = ''): string {
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
}

function getIdFromURL(url: string): string | null {
  try {
    const u = new URL(url)
    const parts = u.pathname.split('/').filter(Boolean)
    // Expect: ['api', 'admin', 'work', 'edit', ':id']
    return parts[4] || null
  } catch {
    return null
  }
}

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const id = getIdFromURL(request.url)

    if (!id) {
      return new Response('Invalid URL', { status: 400 })
    }

    const work = await payload.findByID({ collection: 'work', id })

    if (!work) {
      return new Response('Work item not found', { status: 404 })
    }

    // Try to extract a simple text version of content for textarea
    let contentText = ''
    try {
      const root = (work as any).content?.root
      const firstText = root?.children?.[0]?.children?.[0]?.text
      if (typeof firstText === 'string') contentText = firstText
    } catch {}

    const title = escapeHtml(work.title as string)
    const slug = escapeHtml(work.slug as string)
    const subtitle = escapeHtml((work as any).subtitle || '')
    const date = work.date ? new Date(work.date as string).toISOString().split('T')[0] : ''
    const showOnHomepage = Boolean((work as any).showOnHomepage)
    const passwordLock = Boolean((work as any).passwordLock)
    const password = escapeHtml((work as any).password || '')

    const editHTML = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Edit Work Item - Payload Admin</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { margin: 0; padding: 20px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #09090b; color: white; }
            .container { max-width: 800px; margin: 0 auto; }
            .header { margin-bottom: 30px; padding-bottom: 20px; border-bottom: 1px solid #374151; }
            .back-btn { display: inline-block; padding: 8px 16px; background: #374151; color: white; text-decoration: none; border-radius: 4px; margin-bottom: 20px; }
            .back-btn:hover { background: #4b5563; }
            .form-card { background: #1a1a1a; border-radius: 8px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
            .form-group { margin-bottom: 18px; }
            label { display: block; margin-bottom: 6px; color: #e5e7eb; font-weight: 500; }
            input[type="text"], input[type="date"], textarea { width: 100%; padding: 10px; border-radius: 4px; border: 1px solid #374151; background: #111827; color: white; }
            textarea { min-height: 120px; }
            .checkbox { display: flex; align-items: center; gap: 8px; color: #e5e7eb; }
            .actions { margin-top: 22px; display: flex; gap: 10px; }
            .btn-primary { background: white; color: black; border: none; padding: 10px 16px; border-radius: 4px; cursor: pointer; font-weight: 600; }
            .btn-primary:disabled { opacity: 0.6; cursor: not-allowed; }
            .btn-secondary { background: #374151; color: white; text-decoration: none; padding: 10px 16px; border-radius: 4px; }
            .error { color: #f87171; font-size: 12px; margin-top: 6px; display: none; }
            .success { color: #10b981; font-size: 14px; margin-top: 10px; display: none; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <a href="/api/admin/work/list" class="back-btn">← Back to Work Items</a>
              <h1>Edit Work Item</h1>
            </div>

            <div class="form-card">
              <form id="editForm">
                <div class="form-group">
                  <label for="title">Title *</label>
                  <input type="text" id="title" name="title" value="${title}" required>
                  <div id="titleError" class="error"></div>
                </div>

                <div class="form-group">
                  <label for="slug">Slug *</label>
                  <input type="text" id="slug" name="slug" value="${slug}" required>
                  <div id="slugError" class="error"></div>
                </div>

                <div class="form-group">
                  <label for="subtitle">Subtitle</label>
                  <input type="text" id="subtitle" name="subtitle" value="${subtitle}">
                </div>

                <div class="form-group">
                  <label for="date">Date *</label>
                  <input type="date" id="date" name="date" value="${date}" required>
                  <div id="dateError" class="error"></div>
                </div>

                <div class="form-group">
                  <label for="content">Content</label>
                  <textarea id="content" name="content">${escapeHtml(contentText)}</textarea>
                </div>

                <div class="form-group checkbox">
                  <input type="checkbox" id="showOnHomepage" name="showOnHomepage" ${showOnHomepage ? 'checked' : ''}>
                  <label for="showOnHomepage">Show on Homepage</label>
                </div>

                <div class="form-group checkbox">
                  <input type="checkbox" id="passwordLock" name="passwordLock" ${passwordLock ? 'checked' : ''}>
                  <label for="passwordLock">Password Protected</label>
                </div>

                <div class="form-group" id="passwordGroup" style="display: ${passwordLock ? 'block' : 'none'};">
                  <label for="password">Password</label>
                  <input type="text" id="password" name="password" value="${password}">
                </div>

                <div class="actions">
                  <button type="submit" class="btn-primary submit-btn">Save Changes</button>
                  <a href="/api/admin/work/list" class="btn-secondary">Cancel</a>
                </div>
                <div id="success" class="success">Saved! Redirecting…</div>
              </form>
            </div>
          </div>

          <script>
            const passwordLockEl = document.getElementById('passwordLock');
            const passwordGroup = document.getElementById('passwordGroup');
            passwordLockEl.addEventListener('change', () => {
              passwordGroup.style.display = passwordLockEl.checked ? 'block' : 'none';
            });

            document.getElementById('editForm').addEventListener('submit', async (e) => {
              e.preventDefault();

              const submitBtn = document.querySelector('.submit-btn');
              const successDiv = document.getElementById('success');

              // Clear previous errors
              document.querySelectorAll('.error').forEach(el => el.style.display = 'none');
              successDiv.style.display = 'none';

              const formData = new FormData(e.target);
              const data = {
                title: formData.get('title'),
                slug: formData.get('slug'),
                subtitle: formData.get('subtitle'),
                date: formData.get('date'),
                content: formData.get('content'),
                showOnHomepage: formData.get('showOnHomepage') === 'on',
                passwordLock: formData.get('passwordLock') === 'on',
                password: formData.get('password') || ''
              };

              let hasErrors = false;
              if (!data.title) { document.getElementById('titleError').textContent = 'Title is required'; document.getElementById('titleError').style.display = 'block'; hasErrors = true; }
              if (!data.slug) { document.getElementById('slugError').textContent = 'Slug is required'; document.getElementById('slugError').style.display = 'block'; hasErrors = true; }
              if (!data.date) { document.getElementById('dateError').textContent = 'Date is required'; document.getElementById('dateError').style.display = 'block'; hasErrors = true; }
              if (data.passwordLock && !data.password) { alert('Please provide a password or disable password protection.'); hasErrors = true; }
              if (hasErrors) return;

              submitBtn.disabled = true;
              submitBtn.textContent = 'Saving...';

              try {
                const response = await fetch(window.location.pathname, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify(data)
                });

                if (response.ok) {
                  successDiv.style.display = 'block';
                  setTimeout(() => { window.location.href = '/api/admin/work/list'; }, 1200);
                } else {
                  const errorData = await response.json().catch(() => ({}));
                  alert('Error saving work item' + (errorData.message ? ': ' + errorData.message : ''));
                  submitBtn.disabled = false;
                  submitBtn.textContent = 'Save Changes';
                }
              } catch (err) {
                alert('Network error. Please try again.');
                submitBtn.disabled = false;
                submitBtn.textContent = 'Save Changes';
              }
            });
          </script>
        </body>
      </html>
    `

    return new Response(editHTML, { headers: { 'Content-Type': 'text/html' } })
  } catch (error) {
    console.error('Work edit GET error:', error)
    return new Response('Error loading editor', { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    const id = getIdFromURL(request.url)
    if (!id) return new Response(JSON.stringify({ error: 'Invalid URL' }), { status: 400, headers: { 'Content-Type': 'application/json' } })

    const body = await request.json()

    const formattedContent = {
      root: {
        type: 'root',
        format: '',
        indent: 0,
        version: 1,
        children: [
          {
            type: 'paragraph',
            format: '',
            indent: 0,
            version: 1,
            children: [
              {
                mode: 'normal',
                text: body.content || '',
                type: 'text',
                style: '',
                detail: 0,
                format: 0,
                version: 1,
              },
            ],
          },
        ],
        direction: 'ltr',
      },
    }

    const updated = await payload.update({
      collection: 'work',
      id,
      data: {
        title: body.title,
        slug: body.slug,
        subtitle: body.subtitle || '',
        date: body.date,
        content: formattedContent,
        showOnHomepage: body.showOnHomepage !== false,
        passwordLock: Boolean(body.passwordLock),
        password: body.password || '',
      },
    })

    return new Response(JSON.stringify(updated), { status: 200, headers: { 'Content-Type': 'application/json' } })
  } catch (error: any) {
    console.error('Work edit POST error:', error)
    return new Response(JSON.stringify({ error: 'Failed to update work item', message: error?.message || 'Unknown error' }), { status: 500, headers: { 'Content-Type': 'application/json' } })
  }
}

