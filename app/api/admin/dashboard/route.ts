import { NextRequest } from 'next/server'

export async function GET(request: NextRequest) {
  const dashboardHTML = `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Payload Admin Dashboard</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #09090b;
            color: white;
          }
          .container {
            max-width: 1200px;
            margin: 0 auto;
          }
          .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid #374151;
          }
          .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
          }
          .card {
            background: #1a1a1a;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
          }
          .card h3 {
            margin: 0 0 10px 0;
            color: #60a5fa;
          }
          .card p {
            margin: 0 0 20px 0;
            color: #9ca3af;
          }
          .btn {
            display: inline-block;
            padding: 10px 20px;
            background: white;
            color: black;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
            transition: background 0.2s;
          }
          .btn:hover {
            background: #e5e5e5;
          }
          .logout {
            position: absolute;
            top: 20px;
            right: 20px;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="logout">
            <a href="/api/admin/logout" class="btn">Logout</a>
          </div>

          <div class="header">
            <h1>Payload CMS Dashboard</h1>
            <p>Welcome to your content management system</p>
          </div>

          <div class="grid">
            <div class="card">
              <h3>📝 Posts</h3>
              <p>Manage your blog posts and articles</p>
              <a href="/api/admin/posts/list" class="btn">Manage Posts</a>
            </div>

            <div class="card">
              <h3>💼 Work</h3>
              <p>Showcase your portfolio projects</p>
              <a href="/api/admin/work/list" class="btn">Manage Work</a>
            </div>

            <div class="card">
              <h3>🖼️ Media</h3>
              <p>Upload and manage images</p>
              <a href="/api/admin/media/list" class="btn">Manage Media</a>
            </div>

            <div class="card">
              <h3>📄 Pages</h3>
              <p>Edit page layouts and content</p>
              <a href="/api/admin/pages/list" class="btn">Manage Pages</a>
            </div>

            <div class="card">
              <h3>👥 Users</h3>
              <p>Manage admin users</p>
              <a href="/api/admin/users/list" class="btn">Manage Users</a>
            </div>

            <div class="card">
              <h3>⚙️ Settings</h3>
              <p>Configure your CMS</p>
              <a href="/api/admin/settings" class="btn">Settings</a>
            </div>
          </div>

          <div class="card" style="margin-top: 20px;">
            <h3>🚀 Next Steps</h3>
            <p>Your Payload CMS is now set up! You can start creating content using the collections above.</p>
            <p style="font-size: 14px; color: #6b7280; margin-top: 10px;">
              Visit your website at <a href="/" style="color: #60a5fa;">http://localhost:3000</a>
            </p>
          </div>
        </div>
      </body>
    </html>
  `

  return new Response(dashboardHTML, {
    headers: {
      'Content-Type': 'text/html',
    },
  })
}
