#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { getPayload } from 'payload'
import config from '../payload.config.js'

async function migrateContent() {
  console.log('🚀 Starting content migration to Payload CMS...')

  try {
    const payload = await getPayload({ config })

    // Migrate posts
    await migratePosts(payload)

    // Migrate work items
    await migrateWork(payload)

    console.log('✅ Content migration completed successfully!')

  } catch (error) {
    console.error('❌ Error during migration:', error)
    process.exit(1)
  }
}

async function migratePosts(payload) {
  console.log('📝 Migrating blog posts...')

  const postsDirectory = path.join(process.cwd(), '_posts')

  if (!fs.existsSync(postsDirectory)) {
    console.log('ℹ️ No posts directory found, skipping posts migration')
    return
  }

  const fileNames = fs.readdirSync(postsDirectory).filter(fileName => fileName.endsWith('.md'))

  for (const fileName of fileNames) {
    try {
      const slug = fileName.replace(/\.md$/, '')
      const fullPath = path.join(postsDirectory, fileName)
      const fileContents = fs.readFileSync(fullPath, 'utf8')
      const { data, content } = matter(fileContents)

      // Check if post already exists
      const existingPosts = await payload.find({
        collection: 'posts',
        where: {
          slug: {
            equals: slug,
          },
        },
      })

      if (existingPosts.docs.length > 0) {
        console.log(`⏭️ Post "${slug}" already exists, skipping...`)
        continue
      }

      // Create the post with properly formatted content
      await payload.create({
        collection: 'posts',
        data: {
          title: data.title || slug,
          slug: slug,
          excerpt: data.excerpt || '',
          content: {
            root: {
              type: 'root',
              format: '',
              indent: 0,
              version: 1,
              children: [
                {
                  type: 'paragraph',
                  format: '',
                  indent: 0,
                  version: 1,
                  children: [
                    {
                      mode: 'normal',
                      text: content,
                      type: 'text',
                      style: '',
                      detail: 0,
                      format: 0,
                      version: 1,
                    },
                  ],
                },
              ],
              direction: 'ltr',
            },
          },
          date: data.date ? new Date(data.date).toISOString() : new Date().toISOString(),
          visibility: data.visibility !== false,
          author: data.author || '',
          tags: Array.isArray(data.tags) ? data.tags.map(tag => ({ tag })) : [],
        },
      })

      console.log(`✅ Migrated post: ${data.title || slug}`)

    } catch (error) {
      console.error(`❌ Error migrating post ${fileName}:`, error.message)
    }
  }

  console.log('✅ Posts migration completed')
}

async function migrateWork(payload) {
  console.log('💼 Migrating work items...')

  const workDirectory = path.join(process.cwd(), '_work')

  if (!fs.existsSync(workDirectory)) {
    console.log('ℹ️ No work directory found, skipping work migration')
    return
  }

  const fileNames = fs.readdirSync(workDirectory).filter(fileName => fileName.endsWith('.md'))

  for (const fileName of fileNames) {
    try {
      const slug = fileName.replace(/\.md$/, '')
      const fullPath = path.join(workDirectory, fileName)
      const fileContents = fs.readFileSync(fullPath, 'utf8')
      const { data, content } = matter(fileContents)

      // Check if work item already exists
      const existingWork = await payload.find({
        collection: 'work',
        where: {
          slug: {
            equals: slug,
          },
        },
      })

      if (existingWork.docs.length > 0) {
        console.log(`⏭️ Work item "${slug}" already exists, skipping...`)
        continue
      }

      // For now, skip image upload and use null for coverImage
      // TODO: Implement proper image upload handling
      let coverImageId = null

      // Create the work item with properly formatted content
      await payload.create({
        collection: 'work',
        data: {
          title: data.title || slug,
          slug: slug,
          subtitle: data.subtitle || '',
          coverImage: coverImageId,
          date: data.date ? new Date(data.date).toISOString() : new Date().toISOString(),
          showOnHomepage: data.showOnHomepage !== false,
          passwordLock: Boolean(data.passwordLock),
          password: data.password || '',
          content: {
            root: {
              type: 'root',
              format: '',
              indent: 0,
              version: 1,
              children: [
                {
                  type: 'paragraph',
                  format: '',
                  indent: 0,
                  version: 1,
                  children: [
                    {
                      mode: 'normal',
                      text: content,
                      type: 'text',
                      style: '',
                      detail: 0,
                      format: 0,
                      version: 1,
                    },
                  ],
                },
              ],
              direction: 'ltr',
            },
          },
          gallery: [], // We'll handle gallery images separately if needed
        },
      })

      console.log(`✅ Migrated work item: ${data.title || slug}`)

    } catch (error) {
      console.error(`❌ Error migrating work item ${fileName}:`, error.message)
    }
  }

  console.log('✅ Work items migration completed')
}

migrateContent()
