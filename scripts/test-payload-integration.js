#!/usr/bin/env node

import { getPayload } from 'payload'
import config from '../payload.config.js'
import { debugLog, debugError, debugWarn, measurePerformance } from '../lib/debug-utils.js'

async function testPayloadIntegration() {
  console.log('🧪 Starting Payload CMS Integration Tests...\n')

  let payload = null
  let testResults = {
    passed: 0,
    failed: 0,
    total: 0
  }

  function test(name, testFn) {
    testResults.total++
    console.log(`🔍 Running test: ${name}`)
    try {
      const result = testFn()
      if (result instanceof Promise) {
        return result.then(() => {
          console.log(`✅ PASSED: ${name}\n`)
          testResults.passed++
        }).catch((error) => {
          console.log(`❌ FAILED: ${name}`)
          console.log(`   Error: ${error.message}\n`)
          testResults.failed++
        })
      } else {
        console.log(`✅ PASSED: ${name}\n`)
        testResults.passed++
      }
    } catch (error) {
      console.log(`❌ FAILED: ${name}`)
      console.log(`   Error: ${error.message}\n`)
      testResults.failed++
    }
  }

  try {
    // Test 1: Payload initialization
    await test('Payload CMS Initialization', async () => {
      payload = await getPayload({ config })
      if (!payload) throw new Error('Payload initialization failed')
      debugLog('Payload initialized successfully')
    })

    if (!payload) {
      throw new Error('Cannot continue tests without Payload instance')
    }

    // Test 2: Database connection
    await test('Database Connection', async () => {
      // Try to query users collection
      const users = await payload.find({
        collection: 'users',
        limit: 1
      })
      debugLog('Database connection successful')
    })

    // Test 3: Collections exist
    await test('Collections Setup', async () => {
      const collections = ['users', 'posts', 'work', 'media', 'pages']
      for (const collection of collections) {
        try {
          await payload.find({
            collection,
            limit: 1
          })
          debugLog(`Collection '${collection}' exists`)
        } catch (error) {
          throw new Error(`Collection '${collection}' not found: ${error.message}`)
        }
      }
    })

    // Test 4: Admin user exists
    await test('Admin User Creation', async () => {
      const adminUsers = await payload.find({
        collection: 'users',
        where: {
          email: {
            equals: '<EMAIL>'
          }
        }
      })

      if (adminUsers.docs.length === 0) {
        debugWarn('Admin user not found, creating...')
        await payload.create({
          collection: 'users',
          data: {
            email: '<EMAIL>',
            password: 'password',
            firstName: 'Admin',
            lastName: 'User',
            role: 'admin'
          }
        })
        debugLog('Admin user created')
      } else {
        debugLog('Admin user exists')
      }
    })

    // Test 5: Content migration check
    await test('Content Migration', async () => {
      const posts = await payload.find({ collection: 'posts', limit: 10 })
      const work = await payload.find({ collection: 'work', limit: 10 })

      debugLog(`Found ${posts.docs.length} posts and ${work.docs.length} work items`)

      if (posts.docs.length === 0 && work.docs.length === 0) {
        debugWarn('No content found - migration may be needed')
      }
    })

    // Test 6: Media upload functionality
    await test('Media Upload Setup', async () => {
      // Check if media directory exists
      const fs = await import('fs/promises')
      const path = await import('path')

      const mediaDir = path.join(process.cwd(), 'public', 'media')
      try {
        await fs.access(mediaDir)
        debugLog('Media directory exists')
      } catch (error) {
        debugWarn('Media directory not found, creating...')
        await fs.mkdir(mediaDir, { recursive: true })
        debugLog('Media directory created')
      }
    })

    // Test 7: API endpoints
    await test('API Endpoints', async () => {
      // Test posts API
      const postsResponse = await fetch('http://localhost:3000/api/posts')
      if (!postsResponse.ok) {
        throw new Error(`Posts API failed: ${postsResponse.status}`)
      }

      // Test work API
      const workResponse = await fetch('http://localhost:3000/api/work')
      if (!workResponse.ok) {
        throw new Error(`Work API failed: ${workResponse.status}`)
      }

      debugLog('API endpoints responding correctly')
    })

    // Test 8: Admin panel access
    await test('Admin Panel Access', async () => {
      const adminResponse = await fetch('http://localhost:3000/api/admin/dashboard')
      if (adminResponse.status !== 200) {
        debugWarn(`Admin panel returned ${adminResponse.status} - this may be expected if server is not running`)
      } else {
        debugLog('Admin panel accessible')
      }
    })

    // Test 9: Page rendering
    await test('Page Rendering Setup', async () => {
      const pages = await payload.find({
        collection: 'pages',
        where: {
          slug: {
            equals: 'home'
          }
        }
      })

      if (pages.docs.length === 0) {
        debugWarn('Homepage not found - PageRenderer will use fallback')
      } else {
        debugLog('Homepage found for PageRenderer')
      }
    })

    // Test 10: Performance check
    await test('Performance Check', measurePerformance('Database Query Performance', async () => {
      const start = performance.now()

      // Perform multiple queries to test performance
      await Promise.all([
        payload.find({ collection: 'posts', limit: 5 }),
        payload.find({ collection: 'work', limit: 5 }),
        payload.find({ collection: 'media', limit: 5 }),
        payload.find({ collection: 'pages', limit: 5 })
      ])

      const end = performance.now()
      const duration = end - start

      if (duration > 1000) {
        debugWarn(`Queries took ${duration.toFixed(2)}ms - consider optimization`)
      } else {
        debugLog(`Queries completed in ${duration.toFixed(2)}ms`)
      }
    }))

  } catch (error) {
    debugError('Test suite failed', error)
    testResults.failed++
  }

  // Print results
  console.log('📊 Test Results Summary:')
  console.log(`   ✅ Passed: ${testResults.passed}`)
  console.log(`   ❌ Failed: ${testResults.failed}`)
  console.log(`   📈 Total: ${testResults.total}`)
  console.log(`   📊 Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%\n`)

  if (testResults.failed === 0) {
    console.log('🎉 All tests passed! Payload CMS integration is working correctly.')
  } else {
    console.log('⚠️ Some tests failed. Check the output above for details.')
    console.log('\n🔧 Troubleshooting Tips:')
    console.log('   1. Make sure the development server is running: npm run dev')
    console.log('   2. Check that the database file exists: payload.db')
    console.log('   3. Verify all dependencies are installed: npm install')
    console.log('   4. Run initialization: npm run init-payload')
    console.log('   5. Create homepage: npm run create-homepage')
  }

  console.log('\n🔍 For browser-based debugging:')
  console.log('   1. Open browser developer tools (F12)')
  console.log('   2. Go to Console tab')
  console.log('   3. Run: window.debugPayload.runAllTests()')
  console.log('   4. Check the output for detailed debugging information\n')

  process.exit(testResults.failed > 0 ? 1 : 0)
}

testPayloadIntegration().catch((error) => {
  console.error('❌ Test suite crashed:', error)
  process.exit(1)
})
