#!/usr/bin/env node

import { getPayload } from 'payload'
import config from '../payload.config.js'

async function createDefaultHomepage() {
  console.log('🏠 Creating default homepage...')

  try {
    const payload = await getPayload({ config })

    // Check if homepage already exists
    const existingHomepage = await payload.find({
      collection: 'pages',
      where: {
        slug: {
          equals: 'home',
        },
      },
      limit: 1,
    })

    if (existingHomepage.docs.length > 0) {
      console.log('ℹ️ Homepage already exists')
      return
    }

    // Get some featured work items
    const featuredWork = await payload.find({
      collection: 'work',
      where: {
        showOnHomepage: {
          equals: true,
        },
      },
      limit: 6,
      sort: '-date',
    })

    // Create the homepage with default blocks
    const homepage = await payload.create({
      collection: 'pages',
      data: {
        title: 'Home',
        slug: 'home',
        layout: [
          {
            blockType: 'hero',
            title: 'Welcome to My Portfolio',
            subtitle: 'Creating digital experiences that matter',
            ctaText: 'View My Work',
            ctaLink: '/work',
          },
          {
            blockType: 'workShowcase',
            title: 'Featured Projects',
            description: '<p>Here are some of my recent projects that showcase my skills and passion for creating exceptional digital experiences.</p>',
            featuredWorks: featuredWork.docs.map(work => work.id),
          },
          {
            blockType: 'about',
            title: 'About Me',
            content: '<p>I\'m a passionate developer and designer who loves creating beautiful, functional digital experiences. With expertise in modern web technologies, I bring ideas to life through clean code and thoughtful design.</p><p>When I\'m not coding, you can find me exploring new technologies, contributing to open source projects, or enjoying the outdoors.</p>',
          },
          {
            blockType: 'contact',
            title: 'Let\'s Work Together',
            email: '<EMAIL>',
            socialLinks: [
              {
                platform: 'LinkedIn',
                url: 'https://linkedin.com/in/yourprofile',
              },
              {
                platform: 'GitHub',
                url: 'https://github.com/yourusername',
              },
              {
                platform: 'Twitter',
                url: 'https://twitter.com/yourusername',
              },
            ],
          },
        ],
      },
    })

    console.log('✅ Default homepage created successfully!')
    console.log('📄 Homepage ID:', homepage.id)
    console.log('🔗 Homepage URL: /')

  } catch (error) {
    console.error('❌ Error creating default homepage:', error)
    process.exit(1)
  }
}

createDefaultHomepage()
