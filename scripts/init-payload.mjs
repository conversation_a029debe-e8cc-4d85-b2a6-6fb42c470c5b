#!/usr/bin/env node

import { getPayload } from 'payload'
import config from '../payload.config.js'

async function initPayload() {
  console.log('🚀 Initializing Payload CMS...')

  try {
    const payload = await getPayload({ config })

    // Create admin user if it doesn't exist
    const existingUsers = await payload.find({
      collection: 'users',
      where: {
        email: {
          equals: '<EMAIL>',
        },
      },
    })

    if (existingUsers.docs.length === 0) {
      console.log('👤 Creating admin user...')
      await payload.create({
        collection: 'users',
        data: {
          email: '<EMAIL>',
          password: 'password', // Change this in production!
          firstName: 'Admin',
          lastName: 'User',
          role: 'admin',
        },
      })
      console.log('✅ Admin user created: <EMAIL> / password')
    } else {
      console.log('ℹ️ Admin user already exists')
    }

    console.log('✅ Payload CMS initialized successfully!')
    console.log('🌐 Admin panel: http://localhost:3000/admin')
    console.log('📝 API: http://localhost:3000/api')

  } catch (error) {
    console.error('❌ Error initializing Payload:', error)
    process.exit(1)
  }
}

initPayload()
