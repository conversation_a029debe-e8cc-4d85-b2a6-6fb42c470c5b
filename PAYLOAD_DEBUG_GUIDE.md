# Payload CMS Integration - Debug Guide

## 🎯 **Quick Start Testing**

### **1. Run Automated Tests**
```bash
npm run test-payload
```
This runs 10 comprehensive tests covering:
- ✅ Payload initialization
- ✅ Database connection
- ✅ Collections setup
- ✅ Admin user creation
- ✅ Content migration
- ✅ Media upload setup
- ✅ API endpoints
- ✅ Admin panel access
- ✅ Page rendering
- ✅ Performance checks

### **2. Browser Console Testing**
1. Start your dev server: `npm run dev`
2. Open `http://localhost:3000` in browser
3. Open Developer Tools (F12) → Console tab
4. Run: `window.debugPayload.runAllTests()`

This tests:
- ✅ Payload environment
- ✅ Admin panel access
- ✅ Media API functionality
- ✅ Posts API functionality
- ✅ Work API functionality
- ✅ Page rendering status

## 🔧 **Manual Testing Steps**

### **Admin Panel Testing**
1. Visit `http://localhost:3000/admin`
2. Login with: `<EMAIL>` / `password`
3. Test each button on the dashboard
4. Try creating, editing, and deleting content
5. Upload media files and verify Sharp processing

### **Content Testing**
1. **Posts**: Create/edit posts, check rich text editor
2. **Work**: Upload work items, test gallery functionality
3. **Media**: Upload images, verify responsive variants
4. **Pages**: Edit homepage blocks, test page rendering

### **Frontend Testing**
1. Visit homepage - should show Payload content or fallback
2. Check work/portfolio pages
3. Test blog post pages
4. Verify image loading and optimization

## 🐛 **Common Issues & Solutions**

### **Issue: Admin panel not loading**
```
❌ Admin routes returning 404
```
**Solution:**
```bash
# Check if Payload is initialized
npm run init-payload

# Verify database exists
ls -la payload.db

# Restart dev server
npm run dev
```

### **Issue: Images not processing**
```
❌ Sharp processing failing
```
**Solution:**
```bash
# Check Sharp installation
npm list sharp

# Verify media directory exists
ls -la public/media/

# Test Sharp directly
node -e "const sharp = require('sharp'); console.log('Sharp working')"
```

### **Issue: Content not showing**
```
❌ PageRenderer showing fallback content
```
**Solution:**
```bash
# Create homepage content
npm run create-homepage

# Check if homepage exists
npm run test-payload
```

### **Issue: API endpoints failing**
```
❌ 500 errors on /api/* routes
```
**Solution:**
```bash
# Check server logs
npm run dev

# Test specific endpoints
curl http://localhost:3000/api/posts
curl http://localhost:3000/api/work
```

## 📊 **Debug Commands**

### **Server-Side Debugging**
```bash
# Run with debug logging
DEBUG=* npm run dev

# Test specific components
npm run test-payload

# Check database contents
npx payload db:seed --help
```

### **Browser Console Commands**
```javascript
// Test all Payload functionality
window.debugPayload.runAllTests()

// Test specific APIs
window.debugPayload.testPostsAPI()
window.debugPayload.testMediaAPI()
window.debugPayload.testAdminAccess()

// Check page rendering
window.debugPayload.testPageRender()
```

### **Database Debugging**
```bash
# Check database file
ls -la payload.db

# View database contents (if using SQLite browser)
# Or use Payload admin to inspect collections
```

## 🔍 **Debug Output Examples**

### **Successful Test Output**
```
🧪 Starting Payload CMS Integration Tests...

🔍 Running test: Payload CMS Initialization
✅ PASSED: Payload CMS Initialization

🔍 Running test: Database Connection
✅ PASSED: Database Connection

🔍 Running test: Collections Setup
✅ PASSED: Collections Setup

📊 Test Results Summary:
   ✅ Passed: 10
   ❌ Failed: 0
   📈 Total: 10
   📊 Success Rate: 100.0%

🎉 All tests passed! Payload CMS integration is working correctly.
```

### **Browser Console Output**
```
🔍 Checking Payload status...
✅ Running in browser environment

🔍 Testing admin panel access...
📊 Admin dashboard response: 200
📄 Admin dashboard HTML length: 15432

🔍 Testing media API...
📊 Media API response: 200
📄 Media list HTML length: 8765

✅ All tests completed!
```

## 🚨 **Emergency Troubleshooting**

### **Complete Reset**
```bash
# Stop dev server (Ctrl+C)

# Remove all Payload data
rm -rf payload.db public/media .next

# Clean reinstall
rm -rf node_modules package-lock.json
npm install

# Reinitialize
npm run init-payload
npm run create-homepage

# Restart
npm run dev
```

### **Check System Requirements**
```bash
# Node version
node --version

# Check dependencies
npm list payload sharp

# Disk space
df -h

# Memory
free -h  # (Linux/Mac)
```

## 📞 **Getting Help**

### **Debug Information to Provide**
When reporting issues, include:
1. **Test output**: `npm run test-payload`
2. **Browser console**: `window.debugPayload.runAllTests()`
3. **Server logs**: Dev server output
4. **System info**: Node version, OS, memory
5. **Steps to reproduce**: Exact sequence of actions

### **Quick Health Check**
```bash
# One-command health check
npm run test-payload && echo "✅ System healthy" || echo "❌ Issues detected"
```

## 🎯 **Performance Monitoring**

### **Response Time Monitoring**
```javascript
// In browser console
window.debugPayload.testPostsAPI()  // Should be < 500ms
window.debugPayload.testMediaAPI()  // Should be < 300ms
```

### **Database Query Monitoring**
```bash
# Enable query logging
DEBUG=payload:db:* npm run dev
```

## ✅ **Success Indicators**

Your Payload CMS integration is working correctly when:

- ✅ **All automated tests pass** (`npm run test-payload`)
- ✅ **Admin panel loads** (`/admin` with working login)
- ✅ **Content management works** (create/edit/delete posts)
- ✅ **Media uploads work** (Sharp processing active)
- ✅ **Frontend renders correctly** (PageRenderer or fallback)
- ✅ **API endpoints respond** (200 status codes)
- ✅ **Browser tests pass** (`window.debugPayload.runAllTests()`)

## 🚀 **Next Steps After Debugging**

Once everything is working:

1. **Customize content** - Add your real content
2. **Configure blocks** - Modify block types as needed
3. **Style components** - Adjust styling to match your brand
4. **Add features** - Extend with custom fields/blocks
5. **Deploy** - Set up production environment

---

**🎉 Happy debugging! Your Payload CMS integration includes comprehensive testing and debugging tools.**
