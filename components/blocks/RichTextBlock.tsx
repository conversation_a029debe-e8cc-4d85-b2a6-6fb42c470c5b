import React from 'react'

interface RichTextBlockProps {
  content: any // Rich text content
}

export function RichTextBlock({ content }: RichTextBlockProps) {
  return (
    <section className="py-16 px-4">
      <div className="max-w-screen-lg mx-auto">
        <div className="prose prose-lg prose-invert max-w-none">
          {/* Render rich text content */}
          <div dangerouslySetInnerHTML={{ __html: content }} />
        </div>
      </div>
    </section>
  )
}
