import React from 'react'

interface ContactBlockProps {
  title: string
  email?: string
  phone?: string
  socialLinks?: Array<{
    platform: string
    url: string
  }>
}

export function ContactBlock({ title, email, phone, socialLinks }: ContactBlockProps) {
  return (
    <section className="py-20 px-4">
      <div className="max-w-screen-xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {title}
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-4xl mx-auto">
          {/* Email */}
          {email && (
            <div className="bg-zinc-900 rounded-lg p-8 text-center">
              <div className="text-4xl mb-4">📧</div>
              <h3 className="text-xl font-semibold text-white mb-2">Email</h3>
              <a
                href={`mailto:${email}`}
                className="text-blue-400 hover:text-blue-300 transition-colors"
              >
                {email}
              </a>
            </div>
          )}

          {/* Phone */}
          {phone && (
            <div className="bg-zinc-900 rounded-lg p-8 text-center">
              <div className="text-4xl mb-4">📱</div>
              <h3 className="text-xl font-semibold text-white mb-2">Phone</h3>
              <a
                href={`tel:${phone}`}
                className="text-blue-400 hover:text-blue-300 transition-colors"
              >
                {phone}
              </a>
            </div>
          )}

          {/* Social Links */}
          {socialLinks && socialLinks.length > 0 && (
            <div className="bg-zinc-900 rounded-lg p-8 text-center">
              <div className="text-4xl mb-4">🔗</div>
              <h3 className="text-xl font-semibold text-white mb-4">Connect</h3>
              <div className="flex flex-wrap justify-center gap-4">
                {socialLinks.map((link, index) => (
                  <a
                    key={index}
                    href={link.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-400 hover:text-blue-300 transition-colors text-sm"
                  >
                    {link.platform}
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  )
}
