import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { PayloadWork } from '../../lib/work-payload'

interface WorkShowcaseBlockProps {
  title: string
  description?: any // Rich text content
  featuredWorks?: PayloadWork[]
}

export function WorkShowcaseBlock({ title, description, featuredWorks }: WorkShowcaseBlockProps) {
  return (
    <section className="py-20 px-4">
      <div className="max-w-screen-xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {title}
          </h2>
          {description && (
            <div className="text-lg text-white/80 max-w-3xl mx-auto prose prose-invert">
              {/* Render rich text content */}
              <div dangerouslySetInnerHTML={{ __html: description }} />
            </div>
          )}
        </div>

        {/* Work Grid */}
        {featuredWorks && featuredWorks.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredWorks.map((work) => (
              <Link
                key={work.id}
                href={`/work/${work.slug}`}
                className="group block"
              >
                <div className="bg-zinc-900 rounded-lg overflow-hidden hover:bg-zinc-800 transition-colors">
                  {/* Cover Image */}
                  <div className="relative aspect-video overflow-hidden">
                    <Image
                      src={`/media/${work.coverImage.filename}`}
                      alt={work.coverImage.alt || work.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-blue-400 transition-colors">
                      {work.title}
                    </h3>
                    {work.subtitle && (
                      <p className="text-white/70 mb-4">
                        {work.subtitle}
                      </p>
                    )}
                    <div className="text-sm text-white/50">
                      {new Date(work.date).getFullYear()}
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}

        {/* View All Link */}
        <div className="text-center mt-12">
          <Link
            href="/work"
            className="inline-block px-8 py-3 border border-white/20 text-white hover:bg-white hover:text-black transition-colors rounded-lg"
          >
            View All Work
          </Link>
        </div>
      </div>
    </section>
  )
}
