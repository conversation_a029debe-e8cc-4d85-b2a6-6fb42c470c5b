import React from 'react'
import Image from 'next/image'

interface HeroBlockProps {
  title: string
  subtitle?: string
  backgroundImage?: {
    filename: string
    alt?: string
  }
  ctaText?: string
  ctaLink?: string
}

export function HeroBlock({ title, subtitle, backgroundImage, ctaText, ctaLink }: HeroBlockProps) {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      {backgroundImage && (
        <div className="absolute inset-0 z-0">
          <Image
            src={`/media/${backgroundImage.filename}`}
            alt={backgroundImage.alt || title}
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-black/50" />
        </div>
      )}

      {/* Content */}
      <div className="relative z-10 text-center px-4 max-w-4xl mx-auto">
        <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
          {title}
        </h1>
        {subtitle && (
          <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-2xl mx-auto">
            {subtitle}
          </p>
        )}
        {ctaText && ctaLink && (
          <a
            href={ctaLink}
            className="inline-block px-8 py-4 bg-white text-black font-semibold rounded-lg hover:bg-white/90 transition-colors"
          >
            {ctaText}
          </a>
        )}
      </div>
    </section>
  )
}
