import React from 'react'
import Image from 'next/image'

interface AboutBlockProps {
  title: string
  content: any // Rich text content
  image?: {
    filename: string
    alt?: string
  }
}

export function AboutBlock({ title, content, image }: AboutBlockProps) {
  return (
    <section className="py-20 px-4">
      <div className="max-w-screen-xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="order-2 lg:order-1">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-8">
              {title}
            </h2>
            <div className="prose prose-lg prose-invert max-w-none">
              {/* Render rich text content */}
              <div dangerouslySetInnerHTML={{ __html: content }} />
            </div>
          </div>

          {/* Image */}
          {image && (
            <div className="order-1 lg:order-2">
              <div className="relative aspect-square rounded-lg overflow-hidden">
                <Image
                  src={`/media/${image.filename}`}
                  alt={image.alt || title}
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  )
}
