import React from 'react'
import { <PERSON><PERSON><PERSON> } from './blocks/HeroBlock'
import { WorkShowcaseBlock } from './blocks/WorkShowcaseBlock'
import { AboutBlock } from './blocks/AboutBlock'
import { ContactBlock } from './blocks/ContactBlock'
import { RichTextBlock } from './blocks/RichTextBlock'
import { PayloadPage } from '../lib/pages-payload'

interface PageRendererProps {
  page: PayloadPage
}

export function PageRenderer({ page }: PageRendererProps) {
  return (
    <div>
      {page.layout.map((block, index) => {
        switch (block.blockType) {
          case 'hero':
            return (
              <HeroBlock
                key={index}
                title={block.title}
                subtitle={block.subtitle}
                backgroundImage={block.backgroundImage}
                ctaText={block.ctaText}
                ctaLink={block.ctaLink}
              />
            )

          case 'workShowcase':
            return (
              <WorkShowcaseBlock
                key={index}
                title={block.title}
                description={block.description}
                featuredWorks={block.featuredWorks}
              />
            )

          case 'about':
            return (
              <AboutBlock
                key={index}
                title={block.title}
                content={block.content}
                image={block.image}
              />
            )

          case 'contact':
            return (
              <ContactBlock
                key={index}
                title={block.title}
                email={block.email}
                phone={block.phone}
                socialLinks={block.socialLinks}
              />
            )

          case 'richText':
            return (
              <RichTextBlock
                key={index}
                content={block.content}
              />
            )

          default:
            console.warn(`Unknown block type: ${block.blockType}`)
            return null
        }
      })}
    </div>
  )
}
